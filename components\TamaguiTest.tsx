import React from 'react';
import { YStack, Text, Button } from 'tamagui';

export const TamaguiTest = () => {
  return (
    <YStack padding="$4" space="$4" alignItems="center">
      <Text fontSize="$6" fontWeight="bold" color="$primary">
        Tamagui Configuration Test
      </Text>
      <Text fontSize="$4" color="$gray10">
        If you can see this styled text, Tamagui is working correctly!
      </Text>
      <Button
        backgroundColor="$primary"
        color="white"
        onPress={() => console.log('Tamagui button pressed!')}
      >
        Test Button
      </Button>
    </YStack>
  );
};
