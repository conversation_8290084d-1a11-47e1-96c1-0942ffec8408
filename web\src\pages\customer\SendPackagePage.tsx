import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  MapPin, 
  User, 
  Package, 
  Phone, 
  FileText, 
  Send,
  ArrowRight,
  Truck
} from 'lucide-react';

interface Location {
  lat: number;
  lng: number;
  address: string;
}

interface FormData {
  pickup: Location | null;
  dropoff: Location | null;
  receiverName: string;
  receiverPhone: string;
  packageType: string;
  notes: string;
}

const SendPackagePage: React.FC = () => {
  const [formData, setFormData] = useState<FormData>({
    pickup: null,
    dropoff: null,
    receiverName: '',
    receiverPhone: '',
    packageType: '',
    notes: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const updateField = (field: keyof FormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const isFormValid = () => {
    return formData.pickup && 
           formData.dropoff && 
           formData.receiverName.trim() && 
           formData.receiverPhone.trim() && 
           formData.packageType.trim();
  };

  const handleLocationSelect = (type: 'pickup' | 'dropoff') => {
    // Simulate location selection
    const mockLocation: Location = {
      lat: 32.2211 + Math.random() * 0.01,
      lng: 35.2544 + Math.random() * 0.01,
      address: type === 'pickup' ? 'Nablus City Center' : 'An-Najah University'
    };
    updateField(type, mockLocation);
  };

  const handleSubmit = async () => {
    if (!isFormValid()) return;
    
    setIsSubmitting(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      console.log('Package request submitted:', formData);
      // Navigate to confirmation page
      window.location.href = '/customer/send-package-confirmation';
    } catch (error) {
      console.error('Failed to submit package request:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 relative overflow-hidden">
      {/* Subtle Background Pattern */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-primary-100/20 to-third-100/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-80 h-80 bg-gradient-to-br from-secondary-100/20 to-primary-100/20 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-2xl mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="bg-gradient-to-br from-primary-600 to-secondary-600 rounded-3xl p-8 text-white relative overflow-hidden">
            {/* Decorative Elements */}
            <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full translate-y-12 -translate-x-12"></div>
            
            <div className="relative z-10 text-center">
              <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mx-auto mb-4 backdrop-blur-sm">
                <Truck className="text-white" size={32} />
              </div>
              <h1 className="text-3xl font-bold mb-2">🚚 Send a Package</h1>
              <p className="text-white/80">Fast and reliable package delivery</p>
            </div>
          </div>
        </motion.div>

        {/* Form */}
        <div className="space-y-6">
          {/* Locations Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
              <div className="p-6 border-b border-gray-100">
                <div className="flex items-center gap-3">
                  <div 
                    className="w-12 h-12 rounded-xl flex items-center justify-center"
                    style={{ backgroundColor: '#67B329' }}
                  >
                    <MapPin className="text-white" size={24} />
                  </div>
                  <div>
                    <h3 className="font-bold text-lg text-gray-900">📍 Locations</h3>
                    <p className="text-gray-600 text-sm">Set pickup and delivery locations</p>
                  </div>
                </div>
              </div>

              <div className="p-6 space-y-4">
                {/* Pickup Location */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Pickup Location <span className="text-red-500">*</span>
                  </label>
                  <div className="flex gap-3">
                    <input
                      type="text"
                      value={formData.pickup?.address || ''}
                      placeholder="Set on map"
                      readOnly
                      className="flex-1 px-4 py-3 border border-gray-300 rounded-xl bg-gray-50 text-gray-700 focus:outline-none"
                    />
                    <button
                      onClick={() => handleLocationSelect('pickup')}
                      className="px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-xl transition-colors duration-200"
                    >
                      Set
                    </button>
                  </div>
                </div>

                {/* Dropoff Location */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Drop-off Location <span className="text-red-500">*</span>
                  </label>
                  <div className="flex gap-3">
                    <input
                      type="text"
                      value={formData.dropoff?.address || ''}
                      placeholder="Set on map"
                      readOnly
                      className="flex-1 px-4 py-3 border border-gray-300 rounded-xl bg-gray-50 text-gray-700 focus:outline-none"
                    />
                    <button
                      onClick={() => handleLocationSelect('dropoff')}
                      className="px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-xl transition-colors duration-200"
                    >
                      Set
                    </button>
                  </div>
                </div>

                {/* Route Visualization */}
                {formData.pickup && formData.dropoff && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    className="mt-4 p-4 bg-green-50 border border-green-200 rounded-xl"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span className="text-sm text-green-700 font-medium">Route Set</span>
                      </div>
                      <ArrowRight size={16} className="text-green-600" />
                    </div>
                  </motion.div>
                )}
              </div>
            </div>
          </motion.div>

          {/* Receiver Info Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
              <div className="p-6 border-b border-gray-100">
                <div className="flex items-center gap-3">
                  <div
                    className="w-12 h-12 rounded-xl flex items-center justify-center"
                    style={{ backgroundColor: '#8F3DD2' }}
                  >
                    <User className="text-white" size={24} />
                  </div>
                  <div>
                    <h3 className="font-bold text-lg text-gray-900">👤 Receiver Info</h3>
                    <p className="text-gray-600 text-sm">Who will receive the package?</p>
                  </div>
                </div>
              </div>

              <div className="p-6 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Full Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    placeholder="e.g. Ahmad Jaber"
                    value={formData.receiverName}
                    onChange={(e) => updateField('receiverName', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Phone Number <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="tel"
                    placeholder="e.g. 0599123456"
                    value={formData.receiverPhone}
                    onChange={(e) => updateField('receiverPhone', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>
          </motion.div>

          {/* Package Details Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
              <div className="p-6 border-b border-gray-100">
                <div className="flex items-center gap-3">
                  <div
                    className="w-12 h-12 rounded-xl flex items-center justify-center"
                    style={{ backgroundColor: '#7529B3' }}
                  >
                    <Package className="text-white" size={24} />
                  </div>
                  <div>
                    <h3 className="font-bold text-lg text-gray-900">📦 Package Details</h3>
                    <p className="text-gray-600 text-sm">Describe your package</p>
                  </div>
                </div>
              </div>

              <div className="p-6 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Package Type <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    placeholder="e.g. Fragile, Electronics, Documents"
                    value={formData.packageType}
                    onChange={(e) => updateField('packageType', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Notes (optional)
                  </label>
                  <textarea
                    placeholder="Optional instructions for the driver"
                    value={formData.notes}
                    onChange={(e) => updateField('notes', e.target.value)}
                    rows={3}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
                  />
                </div>
              </div>
            </div>
          </motion.div>

          {/* Submit Button */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="pt-4"
          >
            <button
              onClick={handleSubmit}
              disabled={!isFormValid() || isSubmitting}
              className={`w-full px-6 py-4 font-bold text-lg rounded-xl transition-all duration-200 flex items-center justify-center gap-3 ${
                isFormValid() && !isSubmitting
                  ? 'bg-primary-600 hover:bg-primary-700 text-white shadow-lg hover:shadow-xl transform hover:scale-105'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }`}
            >
              {isSubmitting ? (
                <>
                  <div className="w-6 h-6 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  Sending Request...
                </>
              ) : (
                <>
                  <Send size={24} />
                  Confirm Request
                </>
              )}
            </button>

            {!isFormValid() && (
              <p className="text-center text-sm text-gray-500 mt-3">
                Please fill in all required fields to continue
              </p>
            )}
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default SendPackagePage;
