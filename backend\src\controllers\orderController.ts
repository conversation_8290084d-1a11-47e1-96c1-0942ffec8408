import { Request, Response } from 'express';
import { Order } from '../models/Order';
import { validationResult } from 'express-validator';

export class OrderController {
  // Create a new order
  static async createOrder(req: Request, res: Response) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const userId = req.user?.userId;
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: 'User not authenticated'
        });
      }

      const {
        supplierId,
        supplierName,
        items,
        subtotal,
        deliveryFee,
        totalAmount,
        paymentMethod,
        deliveryAddress,
        notes
      } = req.body;

      // Generate unique order ID
      const orderId = `ORD-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      const order = new Order({
        orderId,
        userId,
        supplierId,
        supplierName,
        items,
        subtotal,
        deliveryFee,
        totalAmount,
        paymentMethod,
        deliveryAddress,
        notes,
        status: 'pending',
        paymentStatus: 'pending'
      });

      await order.save();

      res.status(201).json({
        success: true,
        message: 'Order created successfully',
        data: order
      });
    } catch (error) {
      console.error('Error creating order:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create order',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Get user's orders
  static async getUserOrders(req: Request, res: Response) {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: 'User not authenticated'
        });
      }

      const { page = 1, limit = 20, status } = req.query;
      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);
      const skip = (pageNum - 1) * limitNum;

      const query: any = { userId };
      if (status) {
        query.status = status;
      }

      const orders = await Order.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limitNum)
        .select('-__v');

      const total = await Order.countDocuments(query);

      res.json({
        success: true,
        data: orders,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          pages: Math.ceil(total / limitNum)
        }
      });
    } catch (error) {
      console.error('Error fetching user orders:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch orders',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Get order by ID
  static async getOrderById(req: Request, res: Response) {
    try {
      const { orderId } = req.params;
      const userId = req.user?.userId;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: 'User not authenticated'
        });
      }

      const order = await Order.findOne({ 
        $or: [
          { orderId },
          { _id: orderId }
        ],
        userId 
      }).select('-__v');

      if (!order) {
        return res.status(404).json({
          success: false,
          message: 'Order not found'
        });
      }

      res.json({
        success: true,
        data: order
      });
    } catch (error) {
      console.error('Error fetching order:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch order',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Update order status
  static async updateOrderStatus(req: Request, res: Response) {
    try {
      const { orderId } = req.params;
      const { status, trackingUpdate } = req.body;

      const order = await Order.findOne({ 
        $or: [
          { orderId },
          { _id: orderId }
        ]
      });

      if (!order) {
        return res.status(404).json({
          success: false,
          message: 'Order not found'
        });
      }

      order.status = status;

      if (trackingUpdate) {
        order.trackingUpdates.push({
          status,
          timestamp: new Date(),
          message: trackingUpdate.message,
          location: trackingUpdate.location
        });
      }

      await order.save();

      res.json({
        success: true,
        message: 'Order status updated successfully',
        data: order
      });
    } catch (error) {
      console.error('Error updating order status:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update order status',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }
}
