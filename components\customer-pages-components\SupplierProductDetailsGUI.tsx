import { Stack, useLocalSearchParams, useRouter } from 'expo-router';
import { RestaurantProductDetails } from './product-details/RestaurantProductDetails';
import { ClothingProductDetails } from './product-details/ClothingProductDetails';
import { YStack, Text, Button } from 'tamagui';

type Addition = { id: string; name: string; price: number }

type SupplierDetailsGUIProps = {
    category: string;
}

export function SupplierProductDetailsGUI({ category }: SupplierDetailsGUIProps) {
  const router = useRouter();
  const params = useLocalSearchParams();
  const product: {
    id: string;
    name: string;
    image: string;
    price: number;
    category: string;
    restaurantOptions?: {
      additions?: Addition[];
      without?: string[];
      sides?: Addition[];
    };
    clothingOptions?: {
      sizes: string[];
      colors: string[];
      gallery: string[];
    };
  } | undefined = (() => {
    try {
      return params.product ? JSON.parse(params.product as string) : undefined;
    } catch (error) {
      console.error('Error parsing product:', error);
      return undefined;
    }
  })();

  const title = product?.name ?? 'Item';

  const supplierId = Array.isArray(params.supplierId) ? params.supplierId[0] : params.supplierId;
  const supplierName = Array.isArray(params.supplierName) ? params.supplierName[0] : params.supplierName;

  // Add error boundary and loading state
  if (!product) {
    return (
      <>
        <Stack.Screen options={{ title: 'Product Not Found', headerShown: true }} />
        <YStack flex={1} alignItems="center" justifyContent="center" padding="$4">
          <Text fontSize="$6" fontWeight="bold" color="$gray10">Product Not Found</Text>
          <Text fontSize="$4" color="$gray8" textAlign="center" marginTop="$2">
            The product you're looking for could not be loaded.
          </Text>
          <Button
            marginTop="$4"
            onPress={() => router.back()}
            backgroundColor="$blue10"
          >
            Go Back
          </Button>
        </YStack>
      </>
    );
  }

  return (
    <>
      <Stack.Screen options={{ title, headerShown: true }} />
      {category === 'restaurants' ? (
        <RestaurantProductDetails product={product} supplierId={supplierId} supplierName={supplierName} />
      ) : (
        <ClothingProductDetails product={product} supplierId={supplierId} supplierName={supplierName} />
      )}
    </>
  )
}