import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, MapPin, Phone, Package, Clock, User, FileText } from 'lucide-react';

const RequestPickupPage: React.FC = () => {
  const navigate = useNavigate();
  
  // Form states
  const [pickupAddress, setPickupAddress] = useState('');
  const [deliveryAddress, setDeliveryAddress] = useState('');
  const [senderName, setSenderName] = useState('');
  const [senderPhone, setSenderPhone] = useState('');
  const [receiverName, setReceiverName] = useState('');
  const [receiverPhone, setReceiverPhone] = useState('');
  const [packageType, setPackageType] = useState('');
  const [packageSize, setPackageSize] = useState('small');
  const [packageWeight, setPackageWeight] = useState('');
  const [packageValue, setPackageValue] = useState('');
  const [specialInstructions, setSpecialInstructions] = useState('');
  const [preferredTime, setPreferredTime] = useState('asap');
  const [customTime, setCustomTime] = useState('');

  const packageSizes = [
    { value: 'small', label: 'Small (up to 30cm)', price: 15 },
    { value: 'medium', label: 'Medium (30-60cm)', price: 25 },
    { value: 'large', label: 'Large (60-100cm)', price: 35 },
    { value: 'extra-large', label: 'Extra Large (100cm+)', price: 50 }
  ];

  const packageTypes = [
    'Documents',
    'Electronics',
    'Clothing',
    'Food Items',
    'Fragile Items',
    'Books/Media',
    'Personal Items',
    'Other'
  ];

  const isFormValid = () => {
    return pickupAddress.trim() && 
           deliveryAddress.trim() && 
           senderName.trim() && 
           senderPhone.trim() && 
           receiverName.trim() && 
           receiverPhone.trim() && 
           packageType.trim() &&
           packageWeight.trim();
  };

  const getSelectedSizePrice = () => {
    const size = packageSizes.find(s => s.value === packageSize);
    return size ? size.price : 0;
  };

  const handleSubmit = () => {
    if (!isFormValid()) {
      alert('Please fill in all required fields');
      return;
    }

    const pickupRequest = {
      id: `PU-${Date.now().toString().slice(-6)}`,
      pickupAddress,
      deliveryAddress,
      senderName,
      senderPhone,
      receiverName,
      receiverPhone,
      packageType,
      packageSize,
      packageWeight,
      packageValue,
      specialInstructions,
      preferredTime: preferredTime === 'custom' ? customTime : preferredTime,
      price: getSelectedSizePrice(),
      status: 'Pending',
      createdAt: new Date().toISOString()
    };

    // Navigate to confirmation page
    navigate('/customer/request-pickup-confirmation', { 
      state: { pickupRequest } 
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center gap-4">
            <button
              onClick={() => navigate(-1)}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </button>
            <h1 className="text-2xl font-bold text-gray-900">Request Pickup</h1>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Pickup & Delivery Addresses */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <MapPin className="w-5 h-5" />
                Pickup & Delivery Locations
              </h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Pickup Address *
                  </label>
                  <textarea
                    value={pickupAddress}
                    onChange={(e) => setPickupAddress(e.target.value)}
                    placeholder="Enter pickup address with details..."
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    rows={2}
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Delivery Address *
                  </label>
                  <textarea
                    value={deliveryAddress}
                    onChange={(e) => setDeliveryAddress(e.target.value)}
                    placeholder="Enter delivery address with details..."
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    rows={2}
                    required
                  />
                </div>
              </div>
            </div>

            {/* Contact Information */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <User className="w-5 h-5" />
                Contact Information
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="font-medium text-gray-900">Sender Details</h3>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Sender Name *
                    </label>
                    <input
                      type="text"
                      value={senderName}
                      onChange={(e) => setSenderName(e.target.value)}
                      placeholder="Full name"
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Sender Phone *
                    </label>
                    <input
                      type="tel"
                      value={senderPhone}
                      onChange={(e) => setSenderPhone(e.target.value)}
                      placeholder="+970 XXX XXX XXX"
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      required
                    />
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h3 className="font-medium text-gray-900">Receiver Details</h3>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Receiver Name *
                    </label>
                    <input
                      type="text"
                      value={receiverName}
                      onChange={(e) => setReceiverName(e.target.value)}
                      placeholder="Full name"
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Receiver Phone *
                    </label>
                    <input
                      type="tel"
                      value={receiverPhone}
                      onChange={(e) => setReceiverPhone(e.target.value)}
                      placeholder="+970 XXX XXX XXX"
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      required
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Package Details */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Package className="w-5 h-5" />
                Package Details
              </h2>
              
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Package Type *
                    </label>
                    <select
                      value={packageType}
                      onChange={(e) => setPackageType(e.target.value)}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      required
                    >
                      <option value="">Select package type</option>
                      {packageTypes.map((type) => (
                        <option key={type} value={type}>{type}</option>
                      ))}
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Package Weight *
                    </label>
                    <input
                      type="text"
                      value={packageWeight}
                      onChange={(e) => setPackageWeight(e.target.value)}
                      placeholder="e.g., 2kg, 500g"
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Package Size *
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {packageSizes.map((size) => (
                      <label
                        key={size.value}
                        className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                          packageSize === size.value
                            ? 'border-purple-500 bg-purple-50'
                            : 'border-gray-300 hover:border-gray-400'
                        }`}
                      >
                        <input
                          type="radio"
                          name="packageSize"
                          value={size.value}
                          checked={packageSize === size.value}
                          onChange={(e) => setPackageSize(e.target.value)}
                          className="sr-only"
                        />
                        <div className="font-medium">{size.label}</div>
                        <div className="text-sm text-gray-600">₪{size.price}</div>
                      </label>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Package Value (Optional)
                  </label>
                  <input
                    type="text"
                    value={packageValue}
                    onChange={(e) => setPackageValue(e.target.value)}
                    placeholder="Estimated value in ₪"
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>

            {/* Timing & Instructions */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Clock className="w-5 h-5" />
                Timing & Special Instructions
              </h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Preferred Pickup Time
                  </label>
                  <div className="space-y-2">
                    <label className="flex items-center gap-2">
                      <input
                        type="radio"
                        name="preferredTime"
                        value="asap"
                        checked={preferredTime === 'asap'}
                        onChange={(e) => setPreferredTime(e.target.value)}
                        className="w-4 h-4 text-purple-600"
                      />
                      <span>As soon as possible</span>
                    </label>
                    <label className="flex items-center gap-2">
                      <input
                        type="radio"
                        name="preferredTime"
                        value="today"
                        checked={preferredTime === 'today'}
                        onChange={(e) => setPreferredTime(e.target.value)}
                        className="w-4 h-4 text-purple-600"
                      />
                      <span>Later today</span>
                    </label>
                    <label className="flex items-center gap-2">
                      <input
                        type="radio"
                        name="preferredTime"
                        value="custom"
                        checked={preferredTime === 'custom'}
                        onChange={(e) => setPreferredTime(e.target.value)}
                        className="w-4 h-4 text-purple-600"
                      />
                      <span>Specific time</span>
                    </label>
                  </div>
                  
                  {preferredTime === 'custom' && (
                    <input
                      type="datetime-local"
                      value={customTime}
                      onChange={(e) => setCustomTime(e.target.value)}
                      className="mt-2 w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    />
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
                    <FileText className="w-4 h-4" />
                    Special Instructions (Optional)
                  </label>
                  <textarea
                    value={specialInstructions}
                    onChange={(e) => setSpecialInstructions(e.target.value)}
                    placeholder="Any special handling instructions, fragile items, access codes, etc..."
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    rows={3}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Summary Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 sticky top-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Pickup Summary</h3>
              
              <div className="space-y-3 mb-6">
                <div className="flex justify-between">
                  <span>Service Fee</span>
                  <span>₪{getSelectedSizePrice()}</span>
                </div>
                <hr />
                <div className="flex justify-between text-lg font-semibold">
                  <span>Total</span>
                  <span>₪{getSelectedSizePrice()}</span>
                </div>
              </div>

              <div className="space-y-3 text-sm text-gray-600 mb-6">
                <p>• Free pickup within 2 hours</p>
                <p>• Real-time tracking included</p>
                <p>• Insurance up to ₪500</p>
                <p>• 24/7 customer support</p>
              </div>

              <button
                onClick={handleSubmit}
                disabled={!isFormValid()}
                className={`w-full py-3 rounded-lg font-semibold transition-colors ${
                  isFormValid()
                    ? 'bg-purple-600 text-white hover:bg-purple-700'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                Request Pickup - ₪{getSelectedSizePrice()}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RequestPickupPage;
