module.exports = function (api) {
  api.cache(true);
  let plugins = [];

  // Always include Tamagui babel plugin but with safer configuration
  plugins.push([
    '@tamagui/babel-plugin',
    {
      components: ['tamagui'],
      config: './tamagui.config.ts',
      logTimings: true,
      disableExtraction: process.env.NODE_ENV === 'development',
    }
  ]);

  plugins.push('react-native-reanimated/plugin');

  return {
    presets: ['babel-preset-expo'],
    plugins,
  };
};
