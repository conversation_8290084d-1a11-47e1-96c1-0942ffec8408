module.exports = function (api) {
  api.cache(true);
  let plugins = [];

  // Disable Tamagui babel plugin temporarily to avoid configuration issues
  // This will help identify if the issue is with the babel plugin or the config
  if (process.env.NODE_ENV === 'production') {
    plugins.push([
      '@tamagui/babel-plugin',
      {
        components: ['tamagui'],
        config: './tamagui.config.ts',
        logTimings: true,
      }
    ]);
  }

  plugins.push('react-native-reanimated/plugin');

  return {
    presets: ['babel-preset-expo'],
    plugins,
  };
};
